# Picker组件滑动卡顿优化方案

## 问题分析

在真机运行时，期望薪资和年龄要求的滑动选择器出现卡顿问题，主要原因包括：

### 1. 数据量过大
- 年龄选择器每列包含50个选项（16-65岁）
- 薪资选择器包含大量薪资范围组合
- 每次滑动都需要处理大量DOM元素

### 2. 重复计算
- `onColumnChange` 事件中频繁使用 `Array.from()` 和 `map()` 生成新数组
- 每次列变化都重新计算相同的数据
- 没有缓存机制，导致重复的昂贵计算

### 3. DOM操作频繁
- 每次列变化都调用 `picker.setColumnData()` 更新DOM
- 缺少硬件加速优化
- 没有针对真机滚动的性能优化

## 优化方案

### 1. 数据预计算和缓存

#### 薪资数据缓存
```javascript
// 创建薪资数据缓存，避免重复map操作
const salaryDataCache = new Map()
Object.keys(SALARY_RANGE).forEach(key => {
  if (key !== '面议') {
    salaryDataCache.set(key, SALARY_RANGE[key].map((item: any) => ({ label: item, value: item })))
  }
})
```

#### 年龄数据预计算
```javascript
const ageOptionsCache = (() => {
  const baseOptions = [
    { label: '不限', value: '不限' },
    ...Array.from({ length: 50 }, (_, i) => ({ label: `${i + 16}岁`, value: i + 16 })),
  ]
  
  // 预计算所有可能的年龄范围组合
  const ageRangeCache = new Map()
  for (let startAge = 16; startAge <= 65; startAge++) {
    const endOptions = Array.from({ length: 65 - startAge }, (_, i) => ({
      label: `${startAge + i + 1}岁`,
      value: startAge + i + 1,
    }))
    ageRangeCache.set(startAge, endOptions)
  }
  
  return { baseOptions, ageRangeCache }
})()
```

### 2. 优化列变化处理

#### 薪资选择器优化
```javascript
const onSalaryColumnChange = (picker: any, values: any, columnIndex: any, resolve: any) => {
  if (columnIndex === 0) {
    const selected = values[0]?.value || '面议'

    // 使用预计算的缓存数据
    if (selected === '面议') {
      picker.setColumnData(1, [{ label: '面议', value: '面议' }])
    } else {
      const cachedData = salaryDataCache.get(selected)
      if (cachedData) {
        picker.setColumnData(1, cachedData)
      }
    }
    resolve()
  }
}
```

#### 年龄选择器优化
```javascript
const onAgeColumnChange = (picker: any, values: any, columnIndex: any, resolve: any) => {
  if (columnIndex === 0) {
    const selectedAge = values[0]?.value
    
    if (selectedAge === '不限') {
      // 使用预计算的基础选项
      picker.setColumnData(1, ageOptionsCache.baseOptions)
    } else {
      // 使用预计算的缓存数据
      const startAge = typeof selectedAge === 'number' ? selectedAge : 16
      const cachedEndOptions = ageOptionsCache.ageRangeCache.get(startAge)
      
      if (cachedEndOptions) {
        picker.setColumnData(1, cachedEndOptions)
      } else {
        picker.setColumnData(1, ageOptionsCache.baseOptions)
      }
    }
    resolve()
  }
}
```

### 3. CSS性能优化

```scss
:deep(.wd-picker) {
  // 优化picker滚动性能
  .wd-picker-view {
    /* 启用硬件加速 */
    transform: translateZ(0);
    backface-visibility: hidden;
    /* 优化真机滚动性能 */
    -webkit-overflow-scrolling: touch;
    /* 减少重绘 */
    will-change: transform;
  }
  
  .wd-picker-view-column {
    /* 优化列滚动性能 */
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  
  .wd-picker-view-item {
    /* 优化单个选项渲染性能 */
    transform: translateZ(0);
    backface-visibility: hidden;
    /* 防止文字模糊 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
```

### 4. 组件属性优化

```vue
<wd-picker
  ref="pickerPop"
  v-model="salaryValue"
  :column-change="onSalaryColumnChange"
  :columns="salaryColumns"
  :display-format="salaryDisplayFormat"
  :loading="false"
  :immediate-change="false"
  @confirm="handleSalaryConfirm"
/>
```

## 优化效果

1. **减少计算开销**：通过预计算和缓存，避免了重复的数组生成操作
2. **提升滚动性能**：启用硬件加速，优化真机滚动体验
3. **减少DOM操作**：使用缓存数据，减少不必要的DOM更新
4. **内存优化**：合理使用Map缓存，避免内存泄漏

## 注意事项

1. 缓存数据在组件初始化时生成，确保数据一致性
2. 使用Map而不是Object来存储缓存，提供更好的性能
3. CSS优化主要针对真机环境，在开发工具中效果可能不明显
4. 建议在真机上测试优化效果

## 后续优化建议

1. 考虑使用虚拟滚动技术处理超大数据集
2. 实现懒加载机制，按需加载数据
3. 添加性能监控，跟踪优化效果
4. 考虑使用Web Workers处理复杂计算
